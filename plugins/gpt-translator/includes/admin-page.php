<?php

if (!defined('ABSPATH')) {
    exit; 
}





// $svg_path = plugin_dir_path(__FILE__) . 'assets/icon.svg'; // путь к вашему SVG-файлу
// $svg_content = file_get_contents($svg_path);
// $svg_encoded = 'data:image/svg+xml;base64,' . base64_encode($svg_content);

add_action('admin_menu', function () {
    add_menu_page(
        'GPT Translator',                // Page title
        'GPT Translator',                // Menu title
        'manage_options',                // Access
        'translation_queue',             // Id menu
        'gpt_translator_admin_page',      // page output
        'dashicons-format-status',       // icon
        6
    );
});

// Enqueue scripts and styles for admin page
add_action('admin_enqueue_scripts', function($hook) {
    // Only load on our admin page
    if ($hook !== 'toplevel_page_translation_queue') {
        return;
    }

    // Enqueue JavaScript
    wp_enqueue_script(
        'gpt-translator-command-log-viewer',
        plugin_dir_url(__FILE__) . 'assets/command-log-viewer.js',
        array('jquery'),
        '1.0.0',
        true
    );

    // Enqueue CSS
    wp_enqueue_style(
        'gpt-translator-command-log-viewer',
        plugin_dir_url(__FILE__) . 'assets/command-log-viewer.css',
        array(),
        '1.0.0'
    );

    // Localize script for AJAX
    wp_localize_script('gpt-translator-command-log-viewer', 'gptTranslatorAjax', array(
        'ajaxUrl' => admin_url('admin-ajax.php'),
        'commandLogNonce' => wp_create_nonce('gpt_translator_command_log_nonce'),
        'commandLogDownloadNonce' => wp_create_nonce('gpt_translator_command_log_download_nonce'),
        'strings' => array(
            'loading' => __('Loading...', 'gpt-translator'),
            'error' => __('Error loading command log', 'gpt-translator'),
            'close' => __('Close', 'gpt-translator'),
            'refresh' => __('Refresh', 'gpt-translator'),
            'download' => __('Download', 'gpt-translator'),
            'search' => __('Search', 'gpt-translator'),
            'file_not_found' => __('Command log file not found', 'gpt-translator'),
            'no_results' => __('No results found', 'gpt-translator')
        )
    ));
});


function gpt_translator_admin_page() {
    global $wpdb;

  
    $languages = apply_filters('wpml_active_languages', NULL, ['skip_missing' => 0]);
    $post_types_list = get_post_types(['public' => true], 'objects');

    if (!empty($_POST['form_action']) && $_POST['form_action'] === 'gpt_translator_save_settings') {
           
        $fields = [
            'api_key'              => 'gpt_translator_api_key',
            'assistant_id'         => 'gpt_translator_assistant_id',
            'exclude-words'        => 'gpt_translator_non_translatable_words',
            'exclude-post-type'    => 'gpt_translator_exclude_post_types',
            'include-block-fileds' => 'gpt_translator_translatable_block_fields',
            'post_ids_to_translate'=> 'gpt_translator_post_ids_to_translate',
        ];
    
        foreach ($fields as $field => $option_name) {
            $new_value = isset($_POST[$field]) ? sanitize_textarea_field($_POST[$field]) : '';
            //$new_value = str_replace(' ', '', $new_value); 
            $old_value = get_option($option_name, '');
    
            if ($new_value !== $old_value) {
                update_option($option_name, $new_value);
            }
        }
        echo '<div class="updated notice"><p>Settings saved successfully.</p></div>';
    }
    
    
    

    ?>

    <div class="translater">
    <?php

        if ( isset($_GET['task_action']) && $_GET['task_action'] === 'task_created' ) {
            echo '<div class="notice notice-success">
                    <p>Task created</p>
                </div>';
        }



        if ( isset( $_GET['delete_task'] ) ) {
            $task_id = intval( $_GET['delete_task'] );
            $deleted = gpt_translator_deleteQueueRow($task_id);

            if ( $deleted ) {
                echo '<div class="notice notice-success"><p>Task deleted. Translating will stop after current post translation completed.</p></div>';
            } else {
                echo '<div class="notice notice-error"><p>Unable to delete task</p></div>';
            }
        
        echo "<script>
                (function() {
                    const url = new URL(window.location.href);
                    url.searchParams.delete('delete_task');
                    window.history.replaceState(null, '', url.toString());
                })();
                </script>";
        }
    ?>
        <main class="content container mt-3">
            <div class="d-flex justify-content-between align-items-center">
                <h3>GPT Translator</h3>
                <button type="button" class="btn btn-outline-secondary btn-sm" id="view-command-log-btn">
                    <span class="dashicons dashicons-media-text" style="font-size: 16px; line-height: 1;"></span>
                    View Command Log
                </button>
            </div>
            <ul class="nav nav-tabs mt-3" id="myTab" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="query-tab" data-bs-toggle="tab" data-bs-target="#query-tab-pane" type="button" role="tab" aria-controls="query-tab-pane" aria-selected="true">Tasks</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="translate-tab" data-bs-toggle="tab" data-bs-target="#translate-tab-pane" type="button" role="tab" aria-controls="translate-tab-pane" aria-selected="false">Translate</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings-tab-pane" type="button" role="tab" aria-controls="settings-tab-pane" aria-selected="false">Settings</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="log-tab" data-bs-toggle="tab" data-bs-target="#log-tab-pane" type="button" role="tab" aria-controls="log-tab-pane" aria-selected="false">Log</button>
                </li>
            </ul>
            <div class="tab-content" id="myTabContent">
                <!-- query -->
                <div class="tab-pane active tab-pane--table" id="query-tab-pane" role="tabpanel" aria-labelledby="query-tab" tabindex="0">
                    <?php

                    $table_name = $wpdb->prefix . 'gpt_translator_translation_queue';

                    if ( $wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name ) {
                        echo '<div class="notice notice-error"><p>Translation queue table does not exist. Please create the table first.</p></div>';
                        return;
                    }

                    $query_page = isset($_GET['query_page']) ? max(1, intval($_GET['query_page'])) : 1;
                    $per_page   = 10; 
                    $offset     = ($query_page - 1) * $per_page;

                    $total = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");

                    $tasks = $wpdb->get_results(
                        $wpdb->prepare("SELECT * FROM $table_name ORDER BY id DESC LIMIT %d, %d", $offset, $per_page)
                    );

                    $statusClasses = [
                        'failed'    => 'text-bg-danger',
                        'pending'   => 'text-bg-warning',
                        'completed' => 'text-bg-success',
                        'error'     => 'text-bg-danger',
                        'processing'     => 'text-bg-info'
                    ];

                    $languages = apply_filters('wpml_active_languages', null, array('skip_missing' => 0));

                    if ( empty( $tasks ) ) {
                        echo '<p class="m-3 text-muted">No tasks created</p>';
                    } else {
                        echo '<table class="table table-hover"><thead>
                                <tr>
                                    <th style="width: 80px;">Progress</th>
                                    <th style="width: 100px;">Queue</th>
                                    <th style="width: 120px;">Exclude ids</th>
                                    <th style="width: 90px;">New status</th>
                                    <th style="width: 100px;">Language</th>
                                    <th style="width: 100px;">Task status</th>
                                    <th>Content type</th>
                                    <th style="width: 120px;">Duration</th>
                                    <th style="width: 154px;">Last activity</th>
                                    <th style="width: 154px;">Created</th>
                                    <th style="width: 70px;">Actions</th>
                                </tr>
                            </thead><tbody>';

                            foreach ($tasks as $task) {
                                $labelClass = $statusClasses[$task->status] ?? 'text-bg-light';
                                
                                if ($task->status === 'failed' && !empty($task->error_message)) {
                                    $task->status = 'Error: ' . esc_html($task->error_message);
                                }

                                $task_progress_current = json_decode($task->ids_to_translate, true);
                                if (!is_array($task_progress_current)) {
                                    $task_progress_current = [$task_progress_current];
                                }
                                $task_progress_current_value = count($task_progress_current);
                                $whole_task = json_decode($task->ids_to_translate_task, true);
                                $whole_task_decoded_string = json_encode($task->ids_to_translate_task);
                                if (!is_array($whole_task)) {
                                    $whole_task = [$whole_task];
                                }
                                $whole_task_value = count($whole_task);
                                
                                if ($whole_task_value == 0) {
                                    $percentage = 0; 
                                    $percentage_progress = 0;
                                } else {
                                    $percentage = ($task_progress_current_value / $whole_task_value) * 100; 
                                    $percentage_progress = 100 - round($percentage, 0);
                                }

                                $flag_from_url = $languages[$task->from_language]['country_flag_url'];
                                $flag_to_url = $languages[$task->to_language]['country_flag_url'];

                              

                                $post_type_string = $task->post_type;
                                if (empty($post_type_string)) {
                                    $post_type_string = 'All post types';
                                }
                                $array = json_decode($post_type_string, true);
                                // Оборачиваем в массив, если декод вернул не массив
                                if (!is_array($array)) {
                                    $array = [$array];
                                }
                                $comma_separated_string = implode(', ', $array);

                                $task_actions = (object) [
                                    'Po file'               => $task->translate_po_file,
                                    'Categories'            => $task->translate_categories,
                                    'Menus'                 => $task->translate_menus,
                                    'Theme settings'        => $task->translate_options,
                                    'User descriptions'     => $task->translate_user_desc,
                                    'Borlabs dialog'        => $task->translate_borlabs_dialog,
                                    $comma_separated_string => $comma_separated_string,
                                ];




                                $values_to_display = [];
                                foreach ($task_actions as $property => $value) {
                                    if (!empty($value)) {
                                        $values_to_display[] = $property;
                                    }
                                }
                                $all_tasks_array = implode(', ', $values_to_display);

                                $decoded_array = json_decode($task->ids_to_translate);
                                $decoded_string = json_encode($task->ids_to_translate);
                                $count = count($decoded_array) > 0 ? "<button type='button' class='btn text-primary progress-popover-btn' data-bs-container='body' data-bs-toggle='popover' data-bs-placement='right' title='Ids to translate' data-bs-content='" . $decoded_string . "' style='padding: 8px 0;'>" . count($decoded_array) . "</button>" : 0;
                                $whole_task_count = count($whole_task) > 0 ? "<button type='button' class='btn text-primary progress-popover-btn' data-bs-container='body' data-bs-toggle='popover' data-bs-placement='right' title='Ids to translate' data-bs-content='" . $whole_task_decoded_string . "' style='padding: 8px 0;'>" . count($whole_task) . "</button>" : 0;
                            

                                $idsToExclude = json_decode($task->ids_to_exclude, true);
                                $excludeDisplay = (is_array($idsToExclude) && !empty($idsToExclude)) ? esc_html(implode(', ', $idsToExclude)) : '-';

                                // Используем реальные времена из базы данных
                                $last_update_date = !empty($task->last_update ?? null) ? new DateTime($task->last_update) : null;
                                $created_at_date  = !empty($task->created_at ?? null)  ? new DateTime($task->created_at)  : null;


                                // Рассчитываем duration только если есть обе даты
                                if ($created_at_date && $last_update_date) {
                                    $interval = $created_at_date->diff($last_update_date);

                                    $interval_parts = [];

                                    if ($interval->d > 0) {
                                        $interval_parts[] = $interval->d . ' days';
                                    }

                                    if ($interval->h > 0) {
                                        $interval_parts[] = $interval->h . ' hours';
                                    }

                                    if ($interval->i > 0) {
                                        $interval_parts[] = $interval->i . ' min';
                                    }

                                    // Если интервал меньше минуты, показываем секунды
                                    if (empty($interval_parts) && $interval->s > 0) {
                                        $interval_parts[] = $interval->s . ' sec';
                                    }

                                    $duration = !empty($interval_parts) ? implode(' ', $interval_parts) : '0 sec';
                                } else {
                                    $duration = '-';
                                }

                                echo "<tr id='{$task->id}'>
                                        <td class='text-center'>
                                            {$percentage_progress}%
                                            <div class='progress mt-1' style='height: 2px;'>
                                                <div class='progress-bar' role='progressbar' style='width: {$percentage_progress}%;' aria-valuenow='{$percentage_progress}' aria-valuemin='0' aria-valuemax='100'></div>
                                            </div>
                                        </td>
                                         <td><span class='d-flex align-items-center'>{$count}&nbsp;of&nbsp;{$whole_task_count}</span></td>

                                        <td>{$excludeDisplay}</td>
                                        <td style='text-transform: capitalize;'>{$task->trans_status}</td>

                                        <td><span class='d-inline-block'>
                                            <img width='18' height='12' class='flag' src='" . esc_url( $flag_from_url ) . "' alt='Flag of " . esc_attr( $languages[$task->from_language]['translated_name'] ) . "' data-bs-toggle='tooltip' data-bs-placement='top' title={$languages[$task->from_language]['translated_name']} /> →
                                            <img width='18' height='12' class='flag' src='" . esc_url( $flag_to_url ) . "' alt='Flag of " . esc_attr( $languages[$task->to_language]['translated_name'] ) . "' data-bs-toggle='tooltip' data-bs-placement='top' title={$languages[$task->to_language]['translated_name']} />
                                            </span>
                                        </td>
                                        <td><span class='badge {$labelClass}'>{$task->status}</span></td>
                                        <td style='text-transform: capitalize;'>{$all_tasks_array}</td>
                                        <td>{$duration}</td>
                                        <td>" . ($last_update_date ? $last_update_date->format('j F Y\, H:i') : '-') . "</td>
                                        <td>{$created_at_date->format('j F Y\, H:i')}</td>
                                        <td>  
                                            <span class='d-flex justify-content-end'>"
                                                . (
                                                    !empty($task->translated_posts_ids) 
                                                        ? "<a href='" . esc_url(add_query_arg('download_task', $task->id)) . "' class='btn btn-sm' data-bs-toggle='tooltip' data-bs-placement='top' title='Download posts'>
                                                                <span class='dashicons dashicons-download'></span>
                                                        </a>"
                                                        : ''
                                                )
                                                . "<a href='" . esc_url(add_query_arg('delete_task', $task->id)) . "' class='btn btn-sm' data-bs-toggle='tooltip' data-bs-placement='top' title='Delete' onclick=\"return confirm('Are you sure you want delete this task?');\">
                                                        <span class='dashicons dashicons-trash'></span>
                                                </a>
                                            </span>
                                            </td>
                                    </tr>";
                                    }
                        echo '</tbody></table>';
                    }

                    if ( $total > $per_page ) {
                        $current_url = remove_query_arg('query_page');
                        $pagination_links = paginate_links( array(
                            'base'      => add_query_arg('query_page', '%#%', $current_url),
                            'format'    => '',
                            'current'   => $query_page,
                            'total'     => ceil($total / $per_page),
                            'prev_text' => __('Previous'),
                            'next_text' => __('Next'),
                            'type'      => 'array'
                        ) );
                        if ( is_array( $pagination_links ) ) {
                            echo '<nav class="p-3" aria-label="Page navigation"><ul class="pagination">';
                            foreach ( $pagination_links as $link ) {
                                if ( strpos( $link, 'current' ) !== false ) {
                                    $link = str_replace('page-numbers current', 'page-link', $link);
                                    echo '<li class="page-item active" aria-current="page">' . $link . '</li>';
                                } else {
                                    $link = str_replace('page-numbers', 'page-link', $link);
                                    echo '<li class="page-item">' . $link . '</li>';
                                }
                            }
                            echo '</ul></nav>';
                        }
                    }
                    ?>
                </div>

                <!-- Translate -->
                <div class="tab-pane" id="translate-tab-pane" role="tabpanel" aria-labelledby="translate-tab" tabindex="0">

                   
                    <form method="POST" enctype="multipart/form-data" action="<?php echo esc_url( admin_url('admin-post.php') ); ?>">
                        <?php wp_nonce_field('gpt_translator_translate_post', 'gpt_translator_translate_nonce'); ?>

                        <input type="hidden" name="action" value="translate_post">

                        <div id="post-translation">
                            <h6 class="mb-3">Translating</h6>
                            <div class="row g-3 align-items-end">

                                <div class="col-2 d-flex flex-column">
                                    <label class="form-label field-label">Content</label>
                                    <div class="btn-group" role="group">
                                        <input type="radio" class="btn-check" name="translate_all" value="true" id="all-entities" checked>
                                        <label class="btn btn-outline-secondary" for="all-entities" data-target="all-entities">All</label>

                                        <input type="radio" class="btn-check" name="translate_all" value="false" id="custom-entities">
                                        <label class="btn btn-outline-secondary" for="custom-entities"
                                            data-target="custom-entities">Custom</label>
                                    </div>
                                </div>
                                <div class="col-2">
                                    <label class="form-label field-label" for="from_lang">From language</label>
                                    <select class="form-select" id="from_lang" name="from_lang">
                                        <?php
                                                            if ( ! empty( $languages ) ) {
                                                                foreach ( $languages as $lang ) {
                                                                    echo '<option value="' . esc_attr( $lang['language_code'] ) . '" ' . selected( $lang['language_code'] === 'en', true, false ) . '>' . esc_html( $lang['translated_name'] ) . '</option>';
                                                                }                                                
                                                            }
                                                        ?>
                                    </select>
                                </div>
                                <div class="col-2">
                                    <label class="form-label field-label" for="to_lang">To language</label>
                                    <select class="form-select" id="to_lang" name="to_lang">
                                        <?php
                                                            if ( ! empty( $languages ) ) {
                                                                foreach ( $languages as $lang ) {
                                                                    echo '<option value="' . esc_attr($lang['language_code']) . '">' . esc_html($lang['translated_name']) . '</option>';
                                                                }
                                                            }
                                                        ?>
                                    </select>
                                </div>
                                <div class="col-2">
                                    <label class="form-label field-label" for="trans_status">To status</label>
                                    <select class="form-select" id="trans_status" name="trans_status">
                                        <option value="draft">Draft</option>
                                        <option value="publish" selected>Published</option>
                                    </select>
                                </div>
                                <div class="col-auto">
                                    <button type="submit" class="btn btn-primary" name="submit_translate">Translate</button>
                                </div>
                            </div>

                            <div id="content-all-entities" class="section-content mt-4">

                            <h6 class="mb-3">Create a whole new language version of your site. </h6>
                            <p>The plugin will translate:</p>

                            <ul class="list-group">
                                <li class="list-group-item">1. PO file (Currently you should do it locally and then commit to git)</li>
                                <li class="list-group-item">2. Categories</li>
                                <li class="list-group-item">3. Pages, posts, wiki</li>
                                <li class="list-group-item">4. Menus</li>
                                <li class="list-group-item">5. Authors</li>
                                <li class="list-group-item">6. Banners</li>
                                <li class="list-group-item">7. Cookies dialog</li>
                                <li class="list-group-item">8. Theme options</li>
                            </ul>

                            </div>

                            <div id="content-custom-entities" class="section-content d-none">

                            <h6 class="mb-3 mt-4"> Posts</h6>
                            <div class="btn-group nav-pills mb-2 ps-0" role="tablist">
                                <a class="btn btn-outline-secondary active" data-bs-toggle="pill" href="#content-all-posts">All</a>
                                <a class="btn btn-outline-secondary" data-bs-toggle="pill" href="#content-from-file">From file</a>
                                <a class="btn btn-outline-secondary" data-bs-toggle="pill" href="#content-by-ids">By IDs</a>
                            </div>
                                <div class="tab-content">
                                <div id="content-all-posts" class="tab-pane show active"></div>

                                <div id="content-from-file" class="tab-pane  mb-4 mt-3 col-6">
                                    <label class="form-label field-label">CSV file only. Gets post urls from first column of the file</label>
                                    <input class="form-control" type="file" id="csv_file" name="csv_file">

                                </div>

                                <div id="content-by-ids" class="tab-pane col-6 mt-3  mb-4">
                                    <label class="form-label field-label">Post IDs:</label>
                                    <input class="form-control" type="text" id="post_id" name="post_id" placeholder="73501, 73502, 73503">
                                </div>

                                </div>

                                <div class="row mt-3 mb-4">

                                    <div class="col-2">
                                        <h6 class="mb-3">Overwriting <i class="bi bi-info-circle" data-bs-toggle="tooltip"
                                                data-bs-placement="top"
                                                title="By default if post already have a translated version, it will be skipper. Check it if you want to make a new translation"></i>
                                        </h6>
                                        <label class="form-label me-3" style="margin-top: 6px;">
                                            <input type="checkbox" name="overwrite_posts" value="1" checked="checked">
                                            Overwrite existing
                                        </label>
                                    </div>
                                </div>

                                <div class="row mt-2">
                                    <div class="col-2 d-flex flex-column">
                                        <h6 class="mb-3"> Post types <i class="bi bi-info-circle" data-bs-toggle="tooltip"
                                                data-bs-placement="top"
                                                title="Important: All categories must be translated before you start translating posts."></i>
                                        </h6>

                                        <?php
                                                            $exclude = get_option('gpt_translator_exclude_post_types', '');
                                                            $exclude = explode(",", $exclude);

                                                            

                                                            foreach ( $post_types_list as $pt ) {
                                                                if ( ! in_array( $pt->name, $exclude ) ) {
                                                                    // Use name "post_types[]" so that the selected types are sent as an array
                                                                    echo '<label class="form-label"><input type="checkbox" name="post_types[]" value="' . esc_attr( $pt->name ) . '"> '. esc_html( $pt->label ) .'</label>';
                                                                }
                                                            }
                                                        ?>

                                    </div>

                                    <div class="col-2 d-flex flex-column">
                                        <h6 class="mb-3">Terms</h6>

                                        <label class="form-label">
                                            <input type="checkbox" name="translate_categories" value="1">
                                            Categories
                                        </label>

                                        <label class="form-label">
                                            <input type="checkbox" name="translate_menus" value="1">
                                            Menus <i class="bi bi-info-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                                title="Important: All posts and pages must be translated before you start translating menus."></i>
                                        </label>

                                        <label class="form-label">
                                            <input type="checkbox" name="translate_options" value="1">Theme's Options <i
                                                class="bi bi-info-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                                title="Theme setting - banners on the post page (ex. Repricer, Business Analitics, Lost & Found )"></i>
                                        </label>

                                    </div>
                                    
                                    <div class="col-2 d-flex flex-column">
                                        <h6 class="mb-3">Files</h6>

                                        <label class="form-label">
                                            <input type="checkbox" name="translate_po_file" value="1">PO File <i class="bi bi-info-circle"
                                                data-bs-toggle="tooltip" data-bs-placement="top"
                                                title="Some words translations are saved in the separate file of the Loco Translate plugin"></i>
                                        </label>
                                    </div>

                                    <div class="col-2 d-flex flex-column">
                                        <h6 class="mb-3">Users</h6>

                                        <label class="form-label">
                                            <input type="checkbox" name="translate_user_desc" value="1">Authors descriptions <i
                                                class="bi bi-info-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                                title="Descriptions of the authors on the 'Authors page'"></i></label>
                                    </div>

                                    <div class="col-2 d-flex flex-column">
                                        <h6 class="mb-3">Borlabs Cookies</h6>

                                        <label class="form-label">
                                            <input type="checkbox" name="translate_borlabs_dialog" value="1">
                                            Cookies dialog <i class="bi bi-info-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                                title="You need to re-save Borlabs Cookies 'Dialog & Widget' settings after translations."></i>
                                        </label>
                                    </div>

                                    <div class="col-2 d-flex flex-column">
                                        <h6 class="mb-3">SEO Translation</h6>

                                        <label class="form-label">
                                            <input type="checkbox" name="seo_only_translation" value="1">
                                            SEO Only <i class="bi bi-info-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                                title="Translate only H1, SEO title and Meta description. Does not translate post content."></i>
                                        </label>
                                    </div>
                                </div>

                                <div class="row align-items-end col-auto mb-2 mt-3">

                                    <div class="col-6 mb-3">
                                        <h6 class="mb-3">Exclude post ids</h6>
                                        <input class="form-control" type="text" name="exclude_posts" placeholder="73501, 73502, 73503">
                                    </div>

                                </div>

                            </div>

                        </div>

                    </form>
                </div>
                <!--  Settings -->
                <div class="tab-pane" id="settings-tab-pane" role="tabpanel" aria-labelledby="settings-tab" tabindex="0">
                   
                   

                    <form method="POST" class="mt-4">
                        <?php wp_nonce_field('gpt_translator_save_keys', 'gpt_translator_nonce'); ?>
                        <div class="mb-3">    
                            <label class="form-label field-label">OpenAI API Key:</label><br>
                            <input type="text" class="form-control" id="api_key" name="api_key" value="<?php echo get_option('gpt_translator_api_key', ''); ?>" placeholder="API Key" required>
                        </div>
                        <div class="mb-3">    
                            <label class="form-label field-label">Assistant ID:</label><br>
                            <input type="text" class="form-control" id="assistant_id" name="assistant_id" value="<?php echo get_option('gpt_translator_assistant_id', ''); ?>" placeholder="Assistant ID" required>
                        </div> 
                        <div class="mb-3">    
                            <label class="form-label field-label">Never translate words:</label><br>
                            <textarea name="exclude-words" class="form-control" placeholder="comma separated list" resize height="240"><?php echo get_option('gpt_translator_non_translatable_words', '') ?></textarea>
                        </div> 
                        <div class="mb-3">    
                            <label class="form-label field-label">Exclude post types:</label><br>
                            <textarea name="exclude-post-type" class="form-control"  placeholder="comma separated list" resize height="240"><?php echo get_option('gpt_translator_exclude_post_types', '') ?></textarea>
                        </div> 
                        <div class="mb-3">    
                            <label class="form-label field-label">Gutenberg block fileds to translate:</label><br>
                            <textarea name="include-block-fileds" class="form-control" placeholder="comma separated list" resize height="240"><?php echo get_option('gpt_translator_translatable_block_fields', '') ?></textarea>
                        </div> 

                        <div class="mb-3">    
                            <label class="form-label field-label">Translate All: post IDs to translate:</label><br>
                            <textarea name="post_ids_to_translate" class="form-control" placeholder="comma separated list" resize height="240"><?php echo get_option('gpt_translator_post_ids_to_translate', '') ?></textarea>
                        </div> 
                        
                        <input type="hidden" name="form_action" value="gpt_translator_save_settings">

                        <div class="mt-3">    
                            <button class="btn btn-primary" type="submit">Save settings</button>
                        </div>  
                            
                    </form>
                </div>
                <!-- Log -->
                <div class="tab-pane tab-pane--table" id="log-tab-pane" role="tabpanel" aria-labelledby="log-tab" tabindex="0">
                    <?php
                    $log_table = $wpdb->prefix . 'gpt_translator_translation_log';
                    $log_page  = isset($_GET['log_page']) ? max(1, intval($_GET['log_page'])) : 1;
                    $per_page  = 100;
                    $offset    = ($log_page - 1) * $per_page;

  
                    $total = $wpdb->get_var("SELECT COUNT(*) FROM $log_table");

                    $logs = $wpdb->get_results(
                        $wpdb->prepare("SELECT * FROM $log_table ORDER BY log_time DESC LIMIT %d, %d", $offset, $per_page)
                    );

                    if ( empty( $logs ) ) {
                        echo '<p class="m-3 text-muted">No log entries found</p>';
                    } else {
                        echo '<table class="table table-hover table__logs">';
                        echo '<thead><tr><th style="width: 154px">Time</th><th>Event</th></tr></thead>';
                        echo '<tbody>';
                        foreach ( $logs as $log ) {
                            $log_time = new DateTime($log->log_time);
                            echo "<tr>
                                    <td>{$log_time->format('j F Y\, H:i')}</td>
                                    <td class='message-cell'>{$log->message}</td>
                                </tr>";
                        }
                        echo '</tbody></table>';
                    }


                    if ( $total > $per_page ) {
                        $current_url = remove_query_arg('log_page');
                        $pagination_links = paginate_links( array(
                            'base'      => add_query_arg('log_page', '%#%', $current_url),
                            'format'    => '',
                            'current'   => $log_page,
                            'total'     => ceil($total / $per_page),
                            'prev_text' => __('Previous'),
                            'next_text' => __('Next'),
                            'type'      => 'array'
                        ) );

                        if ( is_array( $pagination_links ) ) {
                            echo '<nav class="p-3" aria-label="Page navigation"><ul class="pagination">';
                            foreach ( $pagination_links as $link ) {
                                if ( strpos( $link, 'current' ) !== false ) {
                                    $link = str_replace('page-numbers current', 'page-link', $link);
                                    echo '<li class="page-item active" aria-current="page">' . $link . '</li>';
                                } else {
                                    $link = str_replace('page-numbers', 'page-link', $link);
                                    echo '<li class="page-item">' . $link . '</li>';
                                }
                            }
                            echo '</ul></nav>';
                        }
                    }
                    ?>
                </div>

            </div>
        </main>
        <div class="notice notice-info mt-3">
            <p>Manual start cron task: <code><?php echo "<a href='" . esc_url( site_url( '/wp-cron.php?doing_wp_cron' ) ) . "' target='blank'>" . esc_url( site_url( '/wp-cron.php?doing_wp_cron' ) ) . "</a>"; ?></code></p>
            <p>Manual start cli command: <code>wp gpt_translator:process_pending</code></p>
        </div>

        <!-- Command Log Modal -->
        <div class="modal fade" id="commandLogModal" tabindex="-1" aria-labelledby="commandLogModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-fullscreen-lg-down modal-xl">
                <div class="modal-content">
                    <div class="modal-header bg-light">
                        <h1 class="modal-title fs-5" id="commandLogModalLabel">
                            <i class="bi bi-file-text me-2"></i>Command Log
                        </h1>
                        <div class="btn-group me-2" role="group">
                            <button type="button" class="btn btn-outline-primary btn-sm" id="refresh-command-log" data-bs-toggle="tooltip" title="Refresh log content">
                                <i class="bi bi-arrow-clockwise"></i>
                                <span class="d-none d-md-inline ms-1">Refresh</span>
                            </button>
                            <button type="button" class="btn btn-outline-success btn-sm" id="download-command-log" data-bs-toggle="tooltip" title="Download log file">
                                <i class="bi bi-download"></i>
                                <span class="d-none d-md-inline ms-1">Download</span>
                            </button>
                            <button type="button" class="btn btn-outline-warning btn-sm" id="clear-search-log" data-bs-toggle="tooltip" title="Clear search">
                                <i class="bi bi-x-circle"></i>
                                <span class="d-none d-md-inline ms-1">Clear</span>
                            </button>
                        </div>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body p-0">
                        <!-- Search and Controls -->
                        <div class="p-3 border-bottom bg-light">
                            <div class="row g-2 align-items-center">
                                <div class="col-md-6">
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-search"></i>
                                        </span>
                                        <input type="text" class="form-control" id="log-search-input" placeholder="Search in logs...">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select form-select-sm" id="log-level-filter">
                                        <option value="">All levels</option>
                                        <option value="ERROR">Errors</option>
                                        <option value="WARNING">Warnings</option>
                                        <option value="INFO">Info</option>
                                        <option value="DEBUG">Debug</option>
                                        <option value="SUCCESS">Success</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="auto-scroll-toggle" checked>
                                        <label class="form-check-label" for="auto-scroll-toggle">
                                            Auto-scroll
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div id="search-results-info" class="mt-2 text-muted small"></div>
                        </div>

                        <!-- Log Content -->
                        <div id="command-log-content" class="position-relative" style="height: 60vh; overflow-y: auto;">
                            <div class="d-flex justify-content-center align-items-center h-100">
                                <div class="text-center text-muted">
                                    <div class="spinner-border mb-3" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <div>Loading command log...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer bg-light">
                        <div class="me-auto">
                            <small class="text-muted" id="log-file-info">
                                <i class="bi bi-file-earmark-text me-1"></i>
                                File: command.log
                            </small>
                        </div>
                        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                            <i class="bi bi-x-lg me-1"></i>Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
}









function handle_translation_form_submission() {
	global $wpdb;


    if (isset($_POST['translate_all']) && $_POST['translate_all'] === 'true') {

        $ids_to_translate_string = get_option('gpt_translator_post_ids_to_translate', '');
        $ids_to_translate = array_map('intval', array_map('trim', explode(',', $ids_to_translate_string)));


        $args = [
            'ids_to_translate'         => $ids_to_translate,
            'from_language'            => sanitize_text_field($_POST['from_lang']),
            'to_language'              => sanitize_text_field($_POST['to_lang']),
            'trans_status'             => sanitize_text_field($_POST['trans_status']),
            'translate_po_file'        => 0,
            'translate_categories'     => 1,
            'translate_posts'          => 1,
            'translate_menus'          => 1,
            'translate_options'        => 1,
            'translate_user_desc'      => 1,
            'translate_borlabs_dialog' => 1,
            'overwrite_posts'          => 1,
            'post_type'                => ['post', 'page', 'wiki'],
        ];
    }
    else {
    


        $post_types = isset($_POST['post_types']) && is_array($_POST['post_types']) ? $_POST['post_types'] : [];

        // Получаем IDs для перевода
        if (!empty($_POST['post_id'])) {
            $ids_to_translate = preg_split('/\s*,\s*/', $_POST['post_id']);
            $ids_to_translate = array_filter($ids_to_translate, 'is_numeric');
            $ids_to_translate = array_map('intval', $ids_to_translate);
        } elseif (!empty($_FILES['csv_file']['tmp_name'])) {
            $ids_to_translate = csv_import_posts($_FILES['csv_file']['tmp_name'], $post_types);
            if (empty($ids_to_translate)) {
                wp_die(__('No posts to translate found in the CSV file.', 'gpt-translator'));
            }
        } else {
            $ids_to_translate = [];
        }

        // Получаем исключённые посты
        if (!empty($_POST['exclude_posts'])) {
            $ids_to_exclude = preg_split('/\s*,\s*/', $_POST['exclude_posts']);
            $ids_to_exclude = array_filter($ids_to_exclude, 'is_numeric');
            $ids_to_exclude = array_map('intval', $ids_to_exclude);
        } else {
            $ids_to_exclude = [];
        }

        // Подготовка аргументов для создания задач
        $args = [
            'ids_to_translate'         => $ids_to_translate,
            'ids_to_exclude'           => $ids_to_exclude,
            'from_language'            => sanitize_text_field($_POST['from_lang']),
            'to_language'              => sanitize_text_field($_POST['to_lang']),
            'trans_status'             => sanitize_text_field($_POST['trans_status']),
            'translate_po_file'        => isset($_POST['translate_po_file']) ? 1 : 0,
            'translate_categories'     => isset($_POST['translate_categories']) ? 1 : 0,
            'translate_posts'          => !empty($post_types) ? 1 : 0,
            'translate_menus'          => isset($_POST['translate_menus']) ? 1 : 0,
            'translate_options'        => isset($_POST['translate_options']) ? 1 : 0,
            'translate_user_desc'      => isset($_POST['translate_user_desc']) ? 1 : 0,
            'translate_borlabs_dialog' => isset($_POST['translate_borlabs_dialog']) ? 1 : 0,
            'seo_only_translation'     => isset($_POST['seo_only_translation']) ? 1 : 0,
            'overwrite_posts'          => isset($_POST['overwrite_posts']) ? 1 : 0,
            'post_type'                => $post_types,
        ];

    }
	// Создание задач
	gpt_translator_create_tasks($args);

	// Перенаправление
	wp_redirect(add_query_arg('task_action', 'task_created', admin_url('admin.php?page=translation_queue')));
	exit;
}






add_action( 'admin_post_translate_post', 'handle_translation_form_submission' );

// AJAX handlers for command log
add_action('wp_ajax_get_command_log_content', 'handle_get_command_log_content');
add_action('wp_ajax_download_command_log', 'handle_download_command_log');

function handle_get_command_log_content() {
    // Check user permissions
    if (!current_user_can('manage_options')) {
        wp_die('Insufficient permissions');
    }

    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'gpt_translator_command_log_nonce')) {
        wp_die('Security check failed');
    }

    $log_file_path = plugin_dir_path(__FILE__) . 'logs/command.log';

    if (!file_exists($log_file_path)) {
        wp_send_json_error('Command log file not found');
    }

    $content = file_get_contents($log_file_path);

    if ($content === false) {
        wp_send_json_error('Unable to read command log file');
    }

    // Format content for display
    $formatted_content = format_command_log_content($content);

    wp_send_json_success(array(
        'content' => $formatted_content,
        'file_size' => size_format(filesize($log_file_path)),
        'file_modified' => date_i18n(get_option('date_format') . ' ' . get_option('time_format'), filemtime($log_file_path))
    ));
}

function handle_download_command_log() {
    // Check user permissions
    if (!current_user_can('manage_options')) {
        wp_die('Insufficient permissions');
    }

    // Verify nonce
    if (!wp_verify_nonce($_GET['nonce'], 'gpt_translator_command_log_download_nonce')) {
        wp_die('Security check failed');
    }

    $log_file_path = plugin_dir_path(__FILE__) . 'logs/command.log';

    if (!file_exists($log_file_path)) {
        wp_die('Command log file not found');
    }

    // Set headers for file download
    header('Content-Type: text/plain');
    header('Content-Disposition: attachment; filename="command.log"');
    header('Content-Length: ' . filesize($log_file_path));

    // Output file content
    readfile($log_file_path);
    exit;
}

function format_command_log_content($content) {
    if (empty(trim($content))) {
        return '<div class="empty-log-state">
                    <i class="bi bi-file-earmark-text"></i>
                    <h5 class="mb-2">Command log is empty</h5>
                    <p class="mb-0">No log entries found. The log file will be populated when translation tasks are running.</p>
                </div>';
    }

    // Split into lines and format
    $lines = explode("\n", $content);
    $formatted_lines = array();

    foreach ($lines as $line_number => $line) {
        if (empty(trim($line))) {
            continue;
        }

        $formatted_line = format_command_log_line($line, $line_number + 1);
        $formatted_lines[] = $formatted_line;
    }

    if (empty($formatted_lines)) {
        return '<div class="empty-log-state">
                    <i class="bi bi-file-earmark-text"></i>
                    <h5 class="mb-2">Command log is empty</h5>
                    <p class="mb-0">No valid log entries found.</p>
                </div>';
    }

    return '<div class="command-log-lines">' . implode("\n", $formatted_lines) . '</div>';
}

function format_command_log_line($line, $line_number) {
    // Add CSS classes based on log level
    $css_class = 'log-line';
    $line_content = esc_html($line);

    // Color coding based on log level with Bootstrap 5 classes
    if (stripos($line, 'ERROR') !== false || stripos($line, 'FATAL') !== false) {
        $css_class .= ' text-danger error';
    } elseif (stripos($line, 'WARNING') !== false || stripos($line, 'WARN') !== false) {
        $css_class .= ' text-warning warning';
    } elseif (stripos($line, 'INFO') !== false) {
        $css_class .= ' text-info info';
    } elseif (stripos($line, 'DEBUG') !== false) {
        $css_class .= ' text-muted debug';
    } elseif (stripos($line, 'SUCCESS') !== false) {
        $css_class .= ' text-success success';
    } else {
        $css_class .= ' text-primary';
    }

    // Extract timestamp if present
    $timestamp = '';
    if (preg_match('/^\[([^\]]+)\]/', $line, $matches)) {
        $timestamp = $matches[1];
        $line_content = esc_html(substr($line, strlen($matches[0]) + 1));
    }

    return '<div class="' . $css_class . '" data-line="' . $line_number . '" data-timestamp="' . esc_attr($timestamp) . '">' .
           '<span class="line-number text-secondary me-2">' . str_pad($line_number, 3, '0', STR_PAD_LEFT) . '</span>' .
           '<span class="line-content">' . $line_content . '</span>' .
           '</div>';
}


function csv_import_posts($file_path, $post_types): array{
	global $wpdb;

	$ids_to_translate = [];
	$not_found_slugs = [];

	if(($handle = fopen($file_path, 'r')) !== false){

		while(($data = fgetcsv($handle)) !== false){
			$post_url = $data[0];

			if(empty($post_url)){
				continue;
			}

			$slug = basename($post_url);

			// Prepare post types for SQL IN clause
			$post_types_placeholders = implode(',', array_fill(0, count($post_types), '%s'));

			// Build the SQL query to find post by slug
			$sql = $wpdb->prepare(
				"SELECT ID FROM {$wpdb->posts} 
                WHERE post_name = %s 
                AND post_type IN ($post_types_placeholders) 
                AND post_status = 'publish' 
                LIMIT 1",
				array_merge([$slug], $post_types)
			);

			$post_id = $wpdb->get_var($sql);

			if($post_id){
				// Get the post ID in the target language using WPML
				$translated_post_id = apply_filters('wpml_object_id', $post_id, get_post_type($post_id), false, $_POST['from_lang']);

				if($translated_post_id){
					$ids_to_translate[] = $translated_post_id;
				}else{
					// If no translation exists in the source language, use original ID
					$ids_to_translate[] = $post_id;
				}
			}else{
				$not_found_slugs[] = $post_url;
			}
		}

		fclose($handle);
	}else{
		$not_found_slugs[] = 'Unable to open the file.';
	}

	// Store not found slugs for debugging
	if(!empty($not_found_slugs)){
		set_transient('csv_import_not_found_slugs', $not_found_slugs, 60);
	}

	return $ids_to_translate;
}
