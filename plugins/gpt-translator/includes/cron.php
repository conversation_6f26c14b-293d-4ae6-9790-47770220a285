<?php

if (!defined('ABSPATH')) {
	exit; // Запрет прямого доступа
}

require_once plugin_dir_path(__FILE__) . 'class-translator.php';




global $wpdb;

$queue_table = $wpdb->prefix . 'gpt_translator_translation_queue';
$chunk_table = $wpdb->prefix . 'gpt_translator_translation_chunks';

/**
 * Создаёт родительскую задачу и её чанки
 */
function gpt_translator_create_tasks($args) {
    global $wpdb, $queue_table, $chunk_table;

    $ids_to_translate = isset($args['ids_to_translate']) && is_array($args['ids_to_translate']) ? array_values($args['ids_to_translate']) : [];
    $ids_to_exclude   = isset($args['ids_to_exclude']) && is_array($args['ids_to_exclude']) ? array_values($args['ids_to_exclude']) : [];
    $post_type        = isset($args['post_type']) ? (array) $args['post_type'] : [];

    $success = $wpdb->insert($queue_table, [
        'ids_to_translate'          => wp_json_encode($ids_to_translate),
        'ids_to_translate_task'     => wp_json_encode($ids_to_translate),
        'ids_to_exclude'            => wp_json_encode($ids_to_exclude),
        'trans_status'              => sanitize_text_field($args['trans_status'] ?? ''),
        'status'                    => 'pending',
        'translate_posts'           => intval($args['translate_posts']),
        'overwrite_posts'           => intval($args['overwrite_posts']),
        'post_type'                 => wp_json_encode($post_type),
        'from_language'             => sanitize_text_field($args['from_language']),
        'to_language'               => sanitize_text_field($args['to_language']),
        'translate_po_file'         => intval($args['translate_po_file']),
        'translate_categories'      => intval($args['translate_categories']),
        'translate_menus'           => intval($args['translate_menus']),
        'translate_options'         => intval($args['translate_options']),
        'translate_user_desc'       => intval($args['translate_user_desc']),
        'translate_borlabs_dialog'  => intval($args['translate_borlabs_dialog']),
        'seo_only_translation'      => intval($args['seo_only_translation'] ?? 0),
        'created_at'                => current_time('mysql'),
        'last_update'               => current_time('mysql'),
    ]);

    if (!$success) {
        print_r(date('Y-m-d H:i:s') . "❌ Task creation error: " . $wpdb->last_error . "\n");
        return false;
    }

    $parent_task_id = $wpdb->insert_id;

    if (intval($args['translate_categories'])) {
        // Создаем чанк для перевода категорий
        createSingleChunk($parent_task_id, ['translate_categories']);
    } elseif (intval($args['translate_posts']) && !empty($ids_to_translate)) {
        // Создаем чанки для постов
        createChunks($parent_task_id, $ids_to_translate);
    } else {
        // Если нет постов и категорий, сразу создаем post-translation чанки
        createPostTranslationChunks($parent_task_id);
    }

    return $parent_task_id;
}



function createChunks($parent_task_id, $ids_to_translate) {
    global $wpdb;
    $chunk_table = $wpdb->prefix . 'gpt_translator_translation_chunks';

    if (empty($ids_to_translate)) {
        $ids_to_translate = ['None'];
    }
    
    $total_tasks = min(10, count($ids_to_translate));

    
    if ($total_tasks < 1) {
        return;
    }
    
    $chunks = array_fill(0, $total_tasks, []);

    // Распределяем ID по задачам
    for ($i = 0; $i < $total_tasks; $i++) {
        $chunks[$i] = [];
    }

    foreach ($ids_to_translate as $index => $id) {
        $chunk_index = $index % $total_tasks;
        $chunks[$chunk_index][] = $id;
    }

    foreach ($chunks as $chunk) {
        if (!empty($chunk)) {
            createSingleChunk($parent_task_id, $chunk);
        }
    }
}


// function gpt_translator_process_pending_chunks($processing_pre_translate = 0) {
//     global $wpdb;
//     $chunk_table = $wpdb->prefix . 'gpt_translator_translation_chunks';

  
//     $pending_chunks = $wpdb->get_results("
//         SELECT id FROM $chunk_table 
//         WHERE status = 'pending' 
//             OR (status = 'processing' AND last_update < NOW() - INTERVAL 3 MINUTE)
//         ORDER BY created_at ASC 
//         LIMIT 5
//     ");

//     if (empty($pending_chunks)) {
//         return;
//     }

//     $plugin_dir = plugin_dir_path(__FILE__);
//     $logs_dir = $plugin_dir . 'logs/';  
//     $log_file = $logs_dir . 'command.log';

   
//     if (!file_exists($logs_dir)) {
//         wp_mkdir_p($logs_dir);
//     }

//     if (!file_exists($log_file)) {
//         file_put_contents($log_file, "=== GPT Translator Command Log ===\n");
//         chmod($log_file, 0644); // set correct permissions
//     }

   


//     $wp_cli_path = '/opt/bitnami/wp-cli/bin/wp'; 

//     foreach ($pending_chunks as $chunk) {
//         $cmd = sprintf(
//             '%s gpt_translator:process_chunk %d %d',
//             escapeshellcmd($wp_cli_path),
//             (int)$chunk->id,
//             (int)$processing_pre_translate
//         );

        
        
//         file_put_contents($log_file, date('Y-m-d H:i:s') . " Executing: $cmd\n", FILE_APPEND);
        
//         exec("$cmd >> $log_file 2>&1 &", $output, $return_var);
        
//         if ($return_var !== 0) {
//             file_put_contents($log_file, date('Y-m-d H:i:s') . " ❌ Failed to start command\n", FILE_APPEND);
//         }
//     }
// }





function processChunk($chunk_id) {
    global $wpdb;
    $chunk_table = $wpdb->prefix . 'gpt_translator_translation_chunks';
    $queue_table = $wpdb->prefix . 'gpt_translator_translation_queue';
    
    $chunk_data = $wpdb->get_row(
        $wpdb->prepare("SELECT * FROM $chunk_table WHERE id = %d", $chunk_id),
        ARRAY_A
    );
    
    if (!$chunk_data) {
        print_r(date('Y-m-d H:i:s') . "❌ Chunk data not found for chunk $chunk_id\n");
        return;
    }
    
    $parent_task_id = $chunk_data['parent_task_id'];
    $chunk_status = $chunk_data['status'];

    $parent_task = $wpdb->get_row(
        $wpdb->prepare("SELECT * FROM $queue_table WHERE id = %d", $parent_task_id),
        ARRAY_A
    );
    
    if (!$parent_task) {
        $wpdb->update($chunk_table, ['status' => 'error'], ['id' => $chunk_id]);
        print_r(date('Y-m-d H:i:s') . "❌ Parent task not found for chunk $chunk_id\n");
        return;
    }

    if($chunk_status == 'pending') { 
        try {
            $updated = $wpdb->query(
                $wpdb->prepare(
                    "UPDATE $chunk_table 
                    SET status = 'processing', last_update = NOW() 
                    WHERE id = %d AND status = 'pending'",
                    $chunk_id
                )
            );
            
            if ($updated === false) {
                print_r(date('Y-m-d H:i:s') . "❌ Failed to lock chunk $chunk_id\n");
                return;
            }
            
            if ($updated === 0) {
                print_r(date('Y-m-d H:i:s') . "Chunk $chunk_id already processed\n");
                return;
            }   

        } catch (Exception $e) {
            print_r(date('Y-m-d H:i:s') . "❌ Transaction failed: " . $e->getMessage() . "\n");
            return;
        }
    }

    try {
        $translator = new SimpleOpenAITranslator(
            $parent_task['to_language'],
            $parent_task['from_language']
        );
        
        $chunk_data_array = json_decode($chunk_data['ids_to_translate'], true) ?: [];
        $action_type = $chunk_data_array[0] ?? '';

        // Дополнительная диагностика
        print_r(date('Y-m-d H:i:s') . "🔍 Processing chunk $chunk_id with data: " . $chunk_data['ids_to_translate'] . "\n");
        print_r(date('Y-m-d H:i:s') . "🔍 Detected action_type: '$action_type'\n");
        
        if ($action_type === 'translate_categories') {
            // Обновляем статус родительской задачи на processing перед началом перевода
            $wpdb->update($queue_table, 
                ['status' => 'processing', 'last_update' => current_time('mysql')], 
                ['id' => $parent_task_id]
            );
            
           
            // Перевод категорий
            $translator->createTranslateCategories();
            
            // Создаем чанки для постов
            $ids_to_translate = json_decode($parent_task['ids_to_translate'], true) ?: [];
            if (!empty($ids_to_translate) && $ids_to_translate !== ['None']) {
                createChunks($parent_task_id, $ids_to_translate);
                print_r(date('Y-m-d H:i:s') . "✅ Post chunks created\n");
            }
        } elseif (in_array($action_type, ['translate_po_file', 'translate_menus', 'translate_options', 'translate_user_desc', 'translate_borlabs_dialog'])) {
            // Post-translation задачи
            print_r(date('Y-m-d H:i:s') . "🚀 Processing post-translation chunk $chunk_id: $action_type\n");

            // Обновляем статус родительской задачи на processing перед началом post-translation шагов
            $wpdb->update($queue_table,
                ['status' => 'processing', 'last_update' => current_time('mysql')],
                ['id' => $parent_task_id]
            );

            $translator->runPostTranslateSteps(
                $action_type === 'translate_po_file' ? 1 : 0,
                $action_type === 'translate_menus' ? 1 : 0,
                $action_type === 'translate_options' ? 1 : 0,
                $action_type === 'translate_user_desc' ? 1 : 0,
                $action_type === 'translate_borlabs_dialog' ? 1 : 0,
                $parent_task_id
            );

            print_r(date('Y-m-d H:i:s') . "✅ Post-translation step '$action_type' completed\n");

            // Обновляем статус родительской задачи на post-translation после завершения post-translation шага
            $wpdb->update($queue_table,
                ['status' => 'post-translation', 'last_update' => current_time('mysql')],
                ['id' => $parent_task_id]
            );
            print_r(date('Y-m-d H:i:s') . "🔄 Updated parent task status to post-translation\n");

        } else {
            // Обычная обработка постов (массив содержит ID постов)

            // Обновляем статус родительской задачи на processing перед началом перевода
            $wpdb->update($queue_table,
                ['status' => 'processing', 'last_update' => current_time('mysql')],
                ['id' => $parent_task_id]
            );

            $post_ids = is_array($chunk_data_array) && !empty($chunk_data_array) ? $chunk_data_array : [];
            $post_ids_excluded = json_decode($parent_task['ids_to_exclude'], true) ?: [];
            $post_types = json_decode($parent_task['post_type'], true) ?: [];

            $seo_only = isset($parent_task['seo_only_translation']) ? intval($parent_task['seo_only_translation']) : 0;

            $translator->doTranslations(
                $post_ids,
                $post_ids_excluded,
                $parent_task['trans_status'],
                $parent_task['translate_posts'],
                $parent_task['overwrite_posts'],
                $post_types,
                $chunk_id,
                $parent_task_id,
                $seo_only
            );
        }

        // Обновляем статус чанка
        $wpdb->update($chunk_table,
            ['status' => 'completed', 'last_update' => current_time('mysql')],
            ['id' => $chunk_id]
        );

        print_r(date('Y-m-d H:i:s') . "✅ Chunk $chunk_id completed\n");

        // Проверяем завершение всей задачи
        $remaining_chunks = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM $chunk_table
                WHERE parent_task_id = %d AND status NOT IN ('completed', 'error')",
                $parent_task_id
            )
        );

        // Дополнительная диагностика всех чанков
        $all_chunks = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT id, status, ids_to_translate FROM $chunk_table WHERE parent_task_id = %d",
                $parent_task_id
            )
        );

        print_r(date('Y-m-d H:i:s') . "🔍 Chunk status for parent task $parent_task_id:\n");
        foreach ($all_chunks as $chunk) {
            $data_preview = substr($chunk->ids_to_translate, 0, 50) . (strlen($chunk->ids_to_translate) > 50 ? '...' : '');
            print_r("  - Chunk {$chunk->id}: {$chunk->status} (data: {$data_preview})\n");
        }
        print_r("  Total chunks: " . count($all_chunks) . ", Remaining: $remaining_chunks\n");

        if ($remaining_chunks == 0) {
            // Получаем актуальный статус родительской задачи
            $current_parent_status = $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT status FROM $queue_table WHERE id = %d",
                    $parent_task_id
                )
            );

            print_r(date('Y-m-d H:i:s') . "🔍 Parent task $parent_task_id status: $current_parent_status\n");
            print_r(date('Y-m-d H:i:s') . "🔍 Remaining chunks: $remaining_chunks\n");

            if ($current_parent_status == 'processing') {
                print_r(date('Y-m-d H:i:s') . "✅ All main chunks completed, creating post-translation chunks\n");
                createPostTranslationChunks($parent_task_id);
            } elseif ($current_parent_status == 'post-translation') {
                // Завершаем задачу
                print_r(date('Y-m-d H:i:s') . "✅ All post-translation chunks completed, marking task as completed\n");
                $result = $wpdb->update($queue_table,
                    ['status' => 'completed', 'last_update' => current_time('mysql')],
                    ['id' => $parent_task_id]
                );

                if ($result !== false) {
                    print_r(date('Y-m-d H:i:s') . "✅ Parent task $parent_task_id marked as completed\n");
                } else {
                    print_r(date('Y-m-d H:i:s') . "❌ Failed to update parent task $parent_task_id: " . $wpdb->last_error . "\n");
                }
            } else {
                print_r(date('Y-m-d H:i:s') . "⚠️ Unexpected parent task status: $current_parent_status (expected 'processing' or 'post-translation')\n");
            }
        } else {
            print_r(date('Y-m-d H:i:s') . "🔍 Still have $remaining_chunks chunks remaining\n");
        }
        
    } catch (Exception $e) {
        $wpdb->update($chunk_table, ['status' => 'error'], ['id' => $chunk_id]);
        $wpdb->update($queue_table, ['status' => 'error'], ['id' => $parent_task_id]);
        print_r(date('Y-m-d H:i:s') . "❌ Error processing chunk $chunk_id: " . $e->getMessage() . "\n");
        return;
    }
}


function createPostTranslationChunks($parent_task_id) {
    global $wpdb;
    $queue_table = $wpdb->prefix . 'gpt_translator_translation_queue';
    
    $parent_task = $wpdb->get_row(
        $wpdb->prepare("SELECT * FROM $queue_table WHERE id = %d", $parent_task_id),
        ARRAY_A
    );
    
    if (!$parent_task) {
        print_r(date('Y-m-d H:i:s') . "❌ Parent task not found for post-translate actions\n");
        return;
    }

    // Проверяем, есть ли уже post-translation чанки для этой задачи
    $chunk_table = $wpdb->prefix . 'gpt_translator_translation_chunks';
    $existing_post_chunks = $wpdb->get_results(
        $wpdb->prepare(
            "SELECT ids_to_translate FROM $chunk_table
            WHERE parent_task_id = %d
            AND (ids_to_translate LIKE '%translate_po_file%'
                OR ids_to_translate LIKE '%translate_menus%'
                OR ids_to_translate LIKE '%translate_options%'
                OR ids_to_translate LIKE '%translate_user_desc%'
                OR ids_to_translate LIKE '%translate_borlabs_dialog%')",
            $parent_task_id
        )
    );

    // Логирование найденных чанков
    print_r(date('Y-m-d H:i:s') . "🔍 Found " . count($existing_post_chunks) . " existing post-translation chunks\n");
    foreach ($existing_post_chunks as $chunk) {
        print_r(date('Y-m-d H:i:s') . "  - Existing chunk data: " . $chunk->ids_to_translate . "\n");
    }

    if (!empty($existing_post_chunks)) {
        print_r(date('Y-m-d H:i:s') . "⚠️ Post-translation chunks already exist, skipping creation\n");

        // Устанавливаем статус post-translation если еще не установлен
        if ($parent_task['status'] !== 'post-translation') {
            $wpdb->update($queue_table,
                ['status' => 'post-translation', 'last_update' => current_time('mysql')],
                ['id' => $parent_task_id]
            );
        }
        return;
    }

    // Собираем список post-translation задач
    $post_tasks = [];
    if (intval($parent_task['translate_po_file'])) $post_tasks[] = 'translate_po_file';
    if (intval($parent_task['translate_menus'])) $post_tasks[] = 'translate_menus';
    if (intval($parent_task['translate_options'])) $post_tasks[] = 'translate_options';
    if (intval($parent_task['translate_user_desc'])) $post_tasks[] = 'translate_user_desc';
    if (intval($parent_task['translate_borlabs_dialog'])) $post_tasks[] = 'translate_borlabs_dialog';

    if (empty($post_tasks)) {
        // Нет post-translation задач, завершаем
        print_r(date('Y-m-d H:i:s') . "✅ No post-translation tasks, marking parent task as completed\n");
        $wpdb->update($queue_table,
            ['status' => 'completed', 'last_update' => current_time('mysql')],
            ['id' => $parent_task_id]
        );
        return;
    }

    // Устанавливаем статус post-translation
    print_r(date('Y-m-d H:i:s') . "🔄 Setting parent task status to post-translation\n");
    $wpdb->update($queue_table,
        ['status' => 'post-translation', 'last_update' => current_time('mysql')],
        ['id' => $parent_task_id]
    );

    // Создаем отдельный чанк для каждой post-translation задачи (без обработки)
    print_r(date('Y-m-d H:i:s') . "📝 Creating " . count($post_tasks) . " post-translation chunks: " . implode(', ', $post_tasks) . "\n");
    foreach ($post_tasks as $task) {
        createSingleChunk($parent_task_id, [$task]);
    }
}

function createSingleChunk($parent_task_id, $ids_array) {
    global $wpdb;
    $chunk_table = $wpdb->prefix . 'gpt_translator_translation_chunks';

    $success = $wpdb->insert($chunk_table, [
        'parent_task_id'    => $parent_task_id,
        'ids_to_translate'  => wp_json_encode($ids_array),
        'status'            => 'pending',
        'created_at'        => current_time('mysql'),
        'last_update'       => current_time('mysql'),
    ]);

    if (!$success) {
        print_r(date('Y-m-d H:i:s') . "❌ Chunk creation error: " . $wpdb->last_error . "\n");
        return false;
    }

    $chunk_id = $wpdb->insert_id;
    print_r(date('Y-m-d H:i:s') . "✅ Created chunk $chunk_id with tasks: " . implode(', ', $ids_array) . "\n");
    return $chunk_id;
}

