<?php

if (!defined('ABSPATH')) {
    exit; // Запрет прямого доступа
}

function gpt_translator_createQueueTable() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'gpt_translator_translation_queue';
    $charset_collate = $wpdb->get_charset_collate();

    if (!wp_next_scheduled('gpt_translator_watchdog')) {
        wp_schedule_event(time(), 'five_minutes', 'gpt_translator_watchdog');
    }



    if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") != $table_name) {
        $sql = "CREATE TABLE $table_name (
            id BIGINT(20) NOT NULL AUTO_INCREMENT,
            ids_to_translate MEDIUMTEXT,
            ids_to_translate_task MEDIUMTEXT,
            ids_to_exclude MEDIUMTEXT,
            translated_posts_ids MEDIUMTEXT,
            last_update DATETIME DEFAULT NULL,
            from_language VARCHAR(10) NOT NULL,
            to_language VARCHAR(10) NOT NULL,
            trans_status TEXT,
            status VARCHAR(20) DEFAULT 'pending',
            translate_po_file TINYINT(1) NOT NULL DEFAULT 0,
            translate_categories TINYINT(1) NOT NULL DEFAULT 0,
            translate_posts TINYINT(1) NOT NULL DEFAULT 0,
            translate_menus TINYINT(1) NOT NULL DEFAULT 0,
            translate_options TINYINT(1) NOT NULL DEFAULT 0,
            translate_user_desc TINYINT(1) NOT NULL DEFAULT 0,
            translate_borlabs_dialog TINYINT(1) NOT NULL DEFAULT 0,
            seo_only_translation TINYINT(1) NOT NULL DEFAULT 0,
            overwrite_posts TINYINT(1) NOT NULL DEFAULT 0,
            post_type VARCHAR(50) NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) $charset_collate;";

        require_once ABSPATH . 'wp-admin/includes/upgrade.php';
        dbDelta($sql);
    } else {
        // Check if seo_only_translation column exists, if not add it
        $column_exists = $wpdb->get_results("SHOW COLUMNS FROM $table_name LIKE 'seo_only_translation'");
        if (empty($column_exists)) {
            $wpdb->query("ALTER TABLE $table_name ADD COLUMN seo_only_translation TINYINT(1) NOT NULL DEFAULT 0 AFTER translate_borlabs_dialog");
        }
    }
}

function gpt_translator_update_table_schema() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'gpt_translator_translation_queue';

    // Check if seo_only_translation column exists, if not add it
    $column_exists = $wpdb->get_results("SHOW COLUMNS FROM $table_name LIKE 'seo_only_translation'");
    if (empty($column_exists)) {
        $wpdb->query("ALTER TABLE $table_name ADD COLUMN seo_only_translation TINYINT(1) NOT NULL DEFAULT 0 AFTER translate_borlabs_dialog");
    }
}

function gpt_translator_createLogTable() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'gpt_translator_translation_log';
    $charset_collate = $wpdb->get_charset_collate();

    if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") != $table_name) {
        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            log_time datetime NOT NULL,
            message text NOT NULL,
            PRIMARY KEY (id)
        ) $charset_collate;";

        require_once ABSPATH . 'wp-admin/includes/upgrade.php';
        dbDelta($sql);
    }

    
}


function gpt_translator_create_chunks_table() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'gpt_translator_translation_chunks';

    $charset_collate = $wpdb->get_charset_collate();

    $sql = "CREATE TABLE $table_name (
        id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
        parent_task_id BIGINT(20) UNSIGNED NOT NULL,
        ids_to_translate MEDIUMTEXT NOT NULL,
        translated_posts_ids MEDIUMTEXT,
        status VARCHAR(20) NOT NULL DEFAULT 'pending',
        created_at DATETIME NOT NULL,
        last_update DATETIME DEFAULT NULL,
        PRIMARY KEY (id),
        KEY parent_task_idx (parent_task_id),
        KEY status_idx (status)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}
register_activation_hook(__FILE__, 'gpt_translator_create_chunks_table');


function gpt_translator_deleteQueueRow($task_id) {
    global $wpdb;

    $queue_table  = $wpdb->prefix . 'gpt_translator_translation_queue';
    $chunks_table = $wpdb->prefix . 'gpt_translator_translation_chunks';

    // Сначала удалим дочерние чанки
    $wpdb->delete(
        $chunks_table,
        array('parent_task_id' => $task_id),
        array('%d')
    );

    // Удаляем основную задачу
    $deleted = $wpdb->delete(
        $queue_table,
        array('id' => $task_id),
        array('%d')
    );

    if (false === $deleted) {
        error_log("Ошибка при удалении основной задачи с id: $task_id");
        return false;
    }

    return true;
}




function download_csv_by_ids($task_id) {
    global $wpdb;

    $data = $wpdb->get_row( $wpdb->prepare(
        "SELECT translated_posts_ids, from_language, to_language, post_type 
         FROM {$wpdb->prefix}gpt_translator_translation_queue 
         WHERE id = %d", $task_id
    ) );
    
    if ( ! $data ) {
        wp_die('Задача не найдена');
    }
    
    $post_ids = json_decode($data->translated_posts_ids, true);
    $from_language = $data->from_language;
    $to_language = $data->to_language;
    $post_type = json_decode($data->post_type, true);
    



    if ( ! is_array($post_ids) || empty($post_ids) ) {
        wp_die('Некорректный формат данных');
    }

    // Очистка буфера перед выводом CSV
    if (ob_get_length()) ob_clean();
    flush();

    // Заголовки для скачивания CSV
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="GPT-translation-results-' . $from_language . '-' . $to_language . '-' . implode('-', $post_type) . '.csv"');
    header('Pragma: no-cache');
    header('Expires: 0');

    $output = fopen('php://output', 'w');

    // Только колонка "Link"
    fputcsv($output, ['Translated URL']);

    foreach ($post_ids as $post_id) {
        $link = get_permalink($post_id);
        fputcsv($output, [ $link ]);
    }

    fclose($output);
    exit;
}



add_action('admin_init', function () {
    if (isset($_GET['download_task'])) {
        $task_id = intval($_GET['download_task']);
        download_csv_by_ids($task_id);
        exit; 
    }
});


