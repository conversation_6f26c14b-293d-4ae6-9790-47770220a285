<?php
/**
 * Database update script for adding seo_only_translation column
 * Run this once to update existing installations
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

global $wpdb;

$table_name = $wpdb->prefix . 'gpt_translator_translation_queue';

// Check if seo_only_translation column exists
$column_exists = $wpdb->get_results("SHOW COLUMNS FROM $table_name LIKE 'seo_only_translation'");

if (empty($column_exists)) {
    $result = $wpdb->query("ALTER TABLE $table_name ADD COLUMN seo_only_translation TINYINT(1) NOT NULL DEFAULT 0 AFTER translate_borlabs_dialog");
    
    if ($result !== false) {
        echo "✅ Successfully added seo_only_translation column to $table_name\n";
    } else {
        echo "❌ Failed to add seo_only_translation column: " . $wpdb->last_error . "\n";
    }
} else {
    echo "ℹ️ Column seo_only_translation already exists in $table_name\n";
}

// Also call the function to ensure table is up to date
require_once('includes/db.php');
gpt_translator_createQueueTable();

echo "✅ Database update completed\n";
